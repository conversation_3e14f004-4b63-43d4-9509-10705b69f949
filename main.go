package main

import (
	"log"
	"mirrorgo/proxy"
	"net/http"
	"os"
)

func main() {
	// 设置路由
	http.HandleFunc("/proxy/", proxy.HandleProxy)

	// 添加相对路径拦截器 - 处理遗漏的相对路径
	http.HandleFunc("/", proxy.HandleRelativePath)

	// 启动服务器
	log.Println("MirrorGo (New Format) is running on :8080")
	log.Println("Proxy path: /proxy/")
	log.Println("新格式访问示例:")
	log.Println("  http://localhost:8080/proxy/https/github.com")
	log.Println("  http://localhost:8080/proxy/http/example.com")
	log.Println("  http://localhost:8080/proxy/https/www.google.com")
	log.Println("")
	log.Println("代理配置说明:")
	log.Println("  设置代理: export MIRRORGO_PROXY=http://127.0.0.1:1087")
	log.Println("  或使用: export HTTP_PROXY=http://127.0.0.1:1087")
	log.Println("  不设置代理时将直接连接目标服务器")

	os.Setenv("http_proxy", "http://127.0.0.1:7890")
	os.Setenv("https_proxy", "http://127.0.0.1:7890")

	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

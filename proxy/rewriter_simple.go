package proxy

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
)

type SimpleURLRewriter struct {
	proxyHost string
	proxyPath string
}

func NewSimpleURLRewriter(proxyHost, proxyPath string) *SimpleURLRewriter {
	return &SimpleURLRewriter{
		proxyHost: proxyHost,
		proxyPath: proxyPath,
	}
}

// RewriteHTML 简单的HTML URL重写
func (r *SimpleURLRewriter) RewriteHTML(content string, baseURL *url.URL) string {
	// 只处理最基本的URL重写，避免复杂的正则表达式

	// 重写绝对URL链接
	content = r.rewriteAbsoluteURLs(content, `href="(https?://[^"]+)"`)
	content = r.rewriteAbsoluteURLs(content, `src="(https?://[^"]+)"`)
	content = r.rewriteAbsoluteURLs(content, `action="(https?://[^"]+)"`)

	// 重写协议相对URL
	content = r.rewriteProtocolRelativeURLs(content, `href="(//[^"]+)"`)
	content = r.rewriteProtocolRelativeURLs(content, `src="(//[^"]+)"`)

	// 重写相对路径URL（基于当前域名）
	if baseURL != nil {
		content = r.rewriteRelativeURLs(content, baseURL, `href="(/[^"]*)"`)
		content = r.rewriteRelativeURLs(content, baseURL, `src="(/[^"]*)"`)
		content = r.rewriteRelativeURLs(content, baseURL, `action="(/[^"]*)"`)
	}

	return content
}

// rewriteAbsoluteURLs 重写绝对URL
func (r *SimpleURLRewriter) rewriteAbsoluteURLs(content, pattern string) string {
	re := regexp.MustCompile(pattern)
	return re.ReplaceAllStringFunc(content, func(match string) string {
		// 提取URL
		submatches := re.FindStringSubmatch(match)
		if len(submatches) < 2 {
			return match
		}

		originalURL := submatches[1]

		// 跳过已经是代理URL的情况
		if strings.Contains(originalURL, r.proxyPath) {
			return match
		}

		// 构造代理URL
		proxyURL := fmt.Sprintf("%s%s%s", r.proxyHost, r.proxyPath, originalURL)

		// 替换原始URL
		return strings.Replace(match, originalURL, proxyURL, 1)
	})
}

// rewriteProtocolRelativeURLs 重写协议相对URL
func (r *SimpleURLRewriter) rewriteProtocolRelativeURLs(content, pattern string) string {
	re := regexp.MustCompile(pattern)
	return re.ReplaceAllStringFunc(content, func(match string) string {
		// 提取URL
		submatches := re.FindStringSubmatch(match)
		if len(submatches) < 2 {
			return match
		}

		originalURL := submatches[1]

		// 跳过已经是代理URL的情况
		if strings.Contains(originalURL, r.proxyPath) {
			return match
		}

		// 转换为完整URL
		fullURL := "https:" + originalURL

		// 构造代理URL
		proxyURL := fmt.Sprintf("%s%s%s", r.proxyHost, r.proxyPath, fullURL)

		// 替换原始URL
		return strings.Replace(match, originalURL, proxyURL, 1)
	})
}

// InjectSimpleJS 注入简单的JavaScript钩子
func (r *SimpleURLRewriter) InjectSimpleJS(htmlContent string) string {
	jsCode := `
<script>
// 简单的URL重写钩子
(function() {
    const PROXY_PATH = '` + r.proxyPath + `';
    const CURRENT_HOST = window.location.origin;

    // 重写函数
    function rewriteURL(url) {
        if (!url || typeof url !== 'string') return url;
        if (url.includes(PROXY_PATH)) return url;
        if (url.startsWith('data:') || url.startsWith('javascript:') || url.startsWith('mailto:')) return url;

        if (url.startsWith('http://') || url.startsWith('https://')) {
            return CURRENT_HOST + PROXY_PATH + url;
        }
        if (url.startsWith('//')) {
            return CURRENT_HOST + PROXY_PATH + 'https:' + url;
        }

        return url;
    }

    // 拦截fetch
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(input, init) {
            if (typeof input === 'string') {
                const rewritten = rewriteURL(input);
                if (rewritten !== input) {
                    console.log('Fetch rewritten:', input, '->', rewritten);
                    return originalFetch.call(this, rewritten, init);
                }
            }
            return originalFetch.call(this, input, init);
        };
    }

    console.log('Simple MirrorGo hook loaded');
})();
</script>
`

	// 在</head>前注入，如果没有则在开头注入
	if strings.Contains(htmlContent, "</head>") {
		return strings.Replace(htmlContent, "</head>", jsCode+"</head>", 1)
	}

	return jsCode + htmlContent
}

// rewriteRelativeURLs 重写相对路径URL
func (r *SimpleURLRewriter) rewriteRelativeURLs(content string, baseURL *url.URL, pattern string) string {
	re := regexp.MustCompile(pattern)
	return re.ReplaceAllStringFunc(content, func(match string) string {
		// 提取URL
		submatches := re.FindStringSubmatch(match)
		if len(submatches) < 2 {
			return match
		}

		originalURL := submatches[1]

		// 跳过已经是代理URL的情况
		if strings.Contains(originalURL, r.proxyPath) {
			return match
		}

		// 跳过特殊路径
		if originalURL == "/" || strings.HasPrefix(originalURL, "/#") || strings.HasPrefix(originalURL, "/?") {
			return match
		}

		// 构造完整URL
		fullURL := fmt.Sprintf("https://%s%s", baseURL.Host, originalURL)

		// 构造代理URL
		proxyURL := fmt.Sprintf("%s%s%s", r.proxyHost, r.proxyPath, fullURL)

		// 替换原始URL
		return strings.Replace(match, originalURL, proxyURL, 1)
	})
}

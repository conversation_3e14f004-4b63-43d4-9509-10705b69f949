package main

import (
	"fmt"
	"io/ioutil"
	"mirrorgo/proxy"
	"net/url"
)

func main() {
	// 读取测试HTML文件
	content, err := ioutil.ReadFile("test_relative_paths.html")
	if err != nil {
		fmt.Printf("Error reading test file: %v\n", err)
		return
	}

	// 创建URL重写器
	rewriter := proxy.NewSimpleURLRewriter("http://localhost:8080", "/proxy/")
	
	// 模拟基础URL (GitHub)
	baseURL, _ := url.Parse("https://github.com/")
	
	// 重写HTML内容
	rewritten := rewriter.RewriteHTML(string(content), baseURL)
	
	fmt.Println("=== Original HTML ===")
	fmt.Println(string(content))
	fmt.Println("\n=== Rewritten HTML ===")
	fmt.Println(rewritten)
	
	// 测试单个URL重写
	testURLs := []string{
		"/assets/mona-sans-d1bf285e9b9b.woff2",
		"/assets/test.css",
		"/assets/test.js",
		"https://github.githubassets.com/assets/app.css",
		"//cdn.example.com/lib.js",
		"relative/path.png",
	}
	
	fmt.Println("\n=== URL Rewriting Tests ===")
	for _, testURL := range testURLs {
		rewritten := rewriter.RewriteURL(testURL, baseURL)
		fmt.Printf("Original: %s\n", testURL)
		fmt.Printf("Rewritten: %s\n", rewritten)
		fmt.Println("---")
	}
}

package main

import (
	"log"
	"mirrorgo/proxy"
	"net/http"
)

func main() {
	// 设置路由
	http.HandleFunc("/proxy/", proxy.HandleProxy)

	// 启动服务器
	log.Println("MirrorGo (Simplified) is running on :8080")
	log.Println("Proxy path: /proxy/")
	log.Println("访问示例: http://localhost:8080/proxy/github.com")

	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

# 🚀 MirrorGo 开发环境使用指南

## 📋 快速开始

### 1. 环境准备
```bash
# 确保已安装Go 1.21+
go version

# 确保配置文件存在
ls config.yaml
```

### 2. 启动服务
```bash
# 方法1: 使用启动脚本
chmod +x start.sh
./start.sh

# 方法2: 直接运行
go mod tidy
go run main_dev.go
```

### 3. 访问控制面板
打开浏览器访问: http://localhost:8080

## 🔧 配置说明

### config.yaml 配置文件
```yaml
# 代理基础配置
proxy:
  mode: "port"                    # 开发环境使用端口模式
  control_port: 8080             # 控制面板端口
  timeout: 30s                   # 请求超时时间

# 上游代理配置（你的VPN）
upstream_proxy:
  enabled: true                  # 启用上游代理
  http_proxy: "http://127.0.0.1:7890"   # HTTP代理地址
  https_proxy: "http://127.0.0.1:7890"  # HTTPS代理地址

# 域名端口映射
domains:
  github.com:
    enabled: true
    backend_port: 8081           # GitHub主站端口
    protocol: https
    related_domains:
      api.github.com:
        backend_port: 8082       # GitHub API端口
      avatars.githubusercontent.com:
        backend_port: 8083       # GitHub头像端口
```

## 🎯 使用方法

### 启动代理
1. **通过控制面板**: http://localhost:8080
2. **直接访问入口**: http://localhost:8080/proxy/github.com
3. **直接访问端口**: http://localhost:8081 (GitHub主站)

### URL重写逻辑
```
原始页面中的链接: https://api.github.com/user/repos
重写后的链接: http://localhost:8082/user/repos
```

### 调试功能
- ✅ **URL重写调试**: 在控制台查看重写日志
- ✅ **访问日志**: 记录所有代理请求
- ✅ **错误日志**: 记录请求失败信息
- ✅ **代理状态**: 显示上游代理连接状态

## 🔍 调试技巧

### 1. 查看日志
```bash
# 启动时会显示详细日志
go run main_dev.go

# 日志示例:
# 🔗 Using upstream proxy: http://127.0.0.1:7890
# [github.com] GET / -> https://github.com/
# 🔄 URL rewrite: api.github.com/user -> localhost:8082/user
```

### 2. 检查端口状态
```bash
# 查看监听端口
netstat -an | grep LISTEN | grep 808

# 应该看到:
# tcp4  0  0  *.8080  *.*  LISTEN  (控制面板)
# tcp4  0  0  *.8081  *.*  LISTEN  (github.com)
# tcp4  0  0  *.8082  *.*  LISTEN  (api.github.com)
```

### 3. 测试代理连接
```bash
# 测试上游代理
curl --proxy http://127.0.0.1:7890 https://github.com

# 测试本地代理
curl http://localhost:8081
```

## 🐛 常见问题

### 1. 端口被占用
```
❌ Server error for github.com on port 8081: listen tcp :8081: bind: address already in use
```
**解决**: 修改config.yaml中的backend_port，或关闭占用端口的程序

### 2. 上游代理连接失败
```
❌ [github.com] Request failed: proxyconnect tcp: dial tcp 127.0.0.1:7890: connect: connection refused
```
**解决**: 检查VPN是否启动，代理地址是否正确

### 3. 配置文件错误
```
❌ Failed to load config: failed to parse config file: yaml: line 10: mapping values are not allowed in this context
```
**解决**: 检查YAML语法，注意缩进和冒号后的空格

### 4. 域名未配置
```
❌ Domain stackoverflow.com not configured
```
**解决**: 在config.yaml的domains部分添加对应域名配置

## 📊 性能监控

### 控制面板功能
- 📈 **实时状态**: 显示所有活跃端口和域名
- 🌐 **域名映射**: 查看完整的端口分配情况
- 🔗 **代理状态**: 监控上游代理连接
- 📝 **快速访问**: 一键启动各种代理

### 日志分析
```bash
# 过滤访问日志
go run main_dev.go 2>&1 | grep "\[.*\]"

# 过滤重写日志
go run main_dev.go 2>&1 | grep "🔄"

# 过滤错误日志
go run main_dev.go 2>&1 | grep "❌"
```

## 🔧 开发技巧

### 1. 修改配置后重启
```bash
# 停止服务 (Ctrl+C)
# 修改 config.yaml
# 重新启动
go run main_dev.go
```

### 2. 添加新域名
```yaml
# 在config.yaml中添加
domains:
  new-site.com:
    enabled: true
    backend_port: 8095
    protocol: https
```

### 3. 调试URL重写
```yaml
# 启用调试模式
rewrite:
  debug_mode: true
```

## 🚀 下一步

开发环境测试完成后，可以：
1. 切换到生产环境配置 (path_prefix模式)
2. 配置Caddy反向代理
3. 部署到服务器
4. 配置HTTPS证书

## 📞 支持

如果遇到问题：
1. 检查日志输出
2. 验证配置文件语法
3. 确认网络连接
4. 查看端口占用情况

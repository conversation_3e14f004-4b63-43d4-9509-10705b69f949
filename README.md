# 🧬 MirrorGo：高性能网站镜像反向代理系统

> 🚀 基于 Golang 编写，专为实现完整网站镜像与加速访问而设计  
> 🎯 目标：绕过封锁、实现动态重写、保持原站体验、稳定可维护

---

## ⚠️ Caddy HTTPS代理架构问题与解决方案

### **问题分析**
当通过Caddy提供HTTPS访问时，URL重写会遇到域名映射问题：

```
用户访问: https://mirror.yourdomain.com/github
后端服务: http://localhost:8081 (GitHub主站)
页面中的链接: https://api.github.com/xxx
重写后: http://localhost:8082/xxx
```

**核心问题**: 用户无法直接访问 `http://localhost:8082`，因为：
1. 用户在HTTPS环境下，浏览器会阻止HTTP请求
2. localhost:8082 对用户不可见（在服务器内部）

### **解决方案：路径映射代替端口映射**

#### **方案A: 路径前缀映射（推荐）**
```yaml
# 配置文件
domains:
  github.com:
    path_prefix: "/github"
    related_domains:
      api.github.com: "/github-api"
      avatars.githubusercontent.com: "/github-avatars"
      github.githubassets.com: "/github-assets"
```

**URL重写规则**:
```
原始: https://api.github.com/user/repos
重写: https://mirror.yourdomain.com/github-api/user/repos
```

**Caddyfile配置**:
```caddy
mirror.yourdomain.com {
    handle /github-api/* {
        reverse_proxy localhost:8082
    }
    handle /github-avatars/* {
        reverse_proxy localhost:8083
    }
    handle /github-assets/* {
        reverse_proxy localhost:8084
    }
    handle /github* {
        reverse_proxy localhost:8081
    }
}
```

#### **方案B: 子域名映射**
```yaml
# 配置文件
domains:
  github.com:
    subdomain: "github"
    related_domains:
      api.github.com: "api-github"
      avatars.githubusercontent.com: "avatars-github"
```

**URL重写规则**:
```
原始: https://api.github.com/user/repos
重写: https://api-github.mirror.yourdomain.com/user/repos
```

**Caddyfile配置**:
```caddy
*.mirror.yourdomain.com {
    @github host github.mirror.yourdomain.com
    handle @github {
        reverse_proxy localhost:8081
    }

    @api-github host api-github.mirror.yourdomain.com
    handle @api-github {
        reverse_proxy localhost:8082
    }
}
```

---

## 🔧 推荐实现方案

### **方案选择建议**
- **开发/测试环境**: 使用端口映射（简单直接）
- **生产环境**: 使用路径前缀映射（用户友好）
- **多站点环境**: 使用子域名映射（隔离性好）

### **配置文件结构**
```yaml
# config.yaml
proxy:
  mode: "path_prefix"  # 可选: "port", "path_prefix", "subdomain"
  base_domain: "mirror.yourdomain.com"

domains:
  github.com:
    enabled: true
    path_prefix: "/github"
    backend_port: 8081
    related_domains:
      api.github.com:
        path_prefix: "/github-api"
        backend_port: 8082
      avatars.githubusercontent.com:
        path_prefix: "/github-avatars"
        backend_port: 8083
      github.githubassets.com:
        path_prefix: "/github-assets"
        backend_port: 8084

  google.com:
    enabled: true
    path_prefix: "/google"
    backend_port: 8085
    related_domains:
      fonts.googleapis.com:
        path_prefix: "/google-fonts"
        backend_port: 8086
```

### **URL重写逻辑**
```go
// 伪代码示例
func rewriteURL(originalURL, mode, baseDomain string) string {
    switch mode {
    case "path_prefix":
        // https://api.github.com/user -> https://mirror.yourdomain.com/github-api/user
        return fmt.Sprintf("https://%s%s%s", baseDomain, getPathPrefix(domain), path)
    case "subdomain":
        // https://api.github.com/user -> https://api-github.mirror.yourdomain.com/user
        return fmt.Sprintf("https://%s.%s%s", getSubdomain(domain), baseDomain, path)
    case "port":
        // https://api.github.com/user -> http://localhost:8082/user (仅开发环境)
        return fmt.Sprintf("http://localhost:%d%s", getPort(domain), path)
    }
}
```

## 竞品对比
- **Ultraviolet**: 客户端重写，复杂度高
- **MirrorGo**: 服务端重写，配置简单


## ✨ 核心特性

- 🌐 **预配置域名映射** - 精确控制代理范围
- 🔄 **智能URL重写** - 支持路径前缀/子域名/端口三种模式
- 🚀 **高性能Go实现** - 原生HTTP库，支持高并发
- 🔐 **Caddy HTTPS集成** - 完美支持SSL证书和反向代理
- 🎯 **核心功能专注** - 只代理重要域名，忽略外部链接
- � **实时监控面板** - Web界面管理和监控
- �🔧 **灵活配置** - YAML配置文件，支持热重载

## 🚀 快速部署指南

### **1. 开发环境（端口模式）**
```bash
# 1. 配置文件
cat > config.yaml << EOF
proxy:
  mode: "port"
  control_port: 8080
domains:
  github.com:
    backend_port: 8081
    related_domains:
      api.github.com:
        backend_port: 8082
EOF

# 2. 启动服务
go run main.go

# 3. 访问测试
curl http://localhost:8080/proxy/github.com
```

### **2. 生产环境（路径前缀模式）**
```bash
# 1. 配置MirrorGo
cat > config.yaml << EOF
proxy:
  mode: "path_prefix"
  base_domain: "mirror.yourdomain.com"
domains:
  github.com:
    path_prefix: "/github"
    backend_port: 8081
    related_domains:
      api.github.com:
        path_prefix: "/github-api"
        backend_port: 8082
EOF

# 2. 配置Caddy
cat > Caddyfile << EOF
mirror.yourdomain.com {
    handle /github-api/* {
        reverse_proxy localhost:8082
    }
    handle /github/* {
        reverse_proxy localhost:8081
    }
    handle /control* {
        reverse_proxy localhost:8080
    }
}
EOF

# 3. 启动服务
docker-compose up -d
```

### **3. Docker Compose完整配置**
```yaml
version: '3.8'
services:
  mirrorgo:
    build: .
    volumes:
      - ./config.yaml:/app/config.yaml
    environment:
      - HTTP_PROXY=http://proxy:7890
    ports:
      - "8080-8100:8080-8100"
    restart: unless-stopped

  caddy:
    image: caddy:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
    depends_on:
      - mirrorgo
    restart: unless-stopped

volumes:
  caddy_data:
```

---
###  页面引用了多个外域资源（如静态资源、头像图像、JS 脚本等），这些也往往被墙
✅ 解决思路：全域名多域反代 + HTML/JS 动态重写

- 为了让页面完整可用，需要做到：

- 自动识别并转发所有相关域名的资源；

- 自动将 HTML、CSS、JS 中引用的 URL 进行“重写”，变成可通过代理访问的形式；

- 最好还要避免使用相对路径导致的跨域问题；

- 对于跳转、表单提交等也要保持代理状态。

## 🔧 关键技术实现要点

### **Caddy HTTPS环境下的URL重写策略**

#### **问题核心**
在Caddy HTTPS代理环境下，传统的端口映射方案会失效：
```
❌ 错误重写: https://api.github.com → http://localhost:8082
✅ 正确重写: https://api.github.com → https://mirror.yourdomain.com/github-api
```

#### **解决方案实现**
```go
// 配置驱动的URL重写
func rewriteURL(originalURL string, config *Config) string {
    domain := extractDomain(originalURL)
    path := extractPath(originalURL)

    // 查找域名配置
    domainConfig := config.FindDomainConfig(domain)
    if domainConfig == nil {
        return originalURL // 保持外部链接不变
    }

    switch config.Proxy.Mode {
    case "path_prefix":
        return fmt.Sprintf("https://%s%s%s",
            config.Proxy.BaseDomain,
            domainConfig.PathPrefix,
            path)
    case "subdomain":
        return fmt.Sprintf("https://%s.%s%s",
            domainConfig.Subdomain,
            config.Proxy.BaseDomain,
            path)
    }
}
```

### **配置文件最佳实践**
```yaml
# 生产环境推荐配置
proxy:
  mode: "path_prefix"
  base_domain: "mirror.yourdomain.com"

domains:
  # GitHub完整配置
  github.com:
    enabled: true
    path_prefix: "/github"
    backend_port: 8081
    related_domains:
      api.github.com:
        path_prefix: "/github-api"
        backend_port: 8082
      avatars.githubusercontent.com:
        path_prefix: "/github-avatars"
        backend_port: 8083
      github.githubassets.com:
        path_prefix: "/github-assets"
        backend_port: 8084
      raw.githubusercontent.com:
        path_prefix: "/github-raw"
        backend_port: 8085

  # 只代理核心域名，忽略外部链接
  # 不包括: twitter.com, discord.com 等社交媒体
```

### **Caddyfile配置模板**
```caddy
# 生产环境Caddy配置
mirror.yourdomain.com {
    # GitHub相关路径 - 按优先级排序
    handle /github-api/* {
        reverse_proxy localhost:8082
    }
    handle /github-avatars/* {
        reverse_proxy localhost:8083
    }
    handle /github-assets/* {
        reverse_proxy localhost:8084
    }
    handle /github-raw/* {
        reverse_proxy localhost:8085
    }
    handle /github* {
        reverse_proxy localhost:8081
    }

    # 控制面板
    handle /control* {
        reverse_proxy localhost:8080
    }

    # 默认重定向
    handle {
        redir /control permanent
    }
}
```

## 🎯 部署检查清单

### **开发环境验证**
- [ ] 配置文件语法正确
- [ ] 所有后端端口正常启动
- [ ] URL重写规则测试通过
- [ ] 外部链接保持不变

### **生产环境验证**
- [ ] Caddy配置与MirrorGo配置匹配
- [ ] HTTPS证书正常工作
- [ ] 所有路径前缀正确映射
- [ ] 跨域资源正常加载
- [ ] 表单提交功能正常

### **性能监控**
- [ ] 响应时间监控
- [ ] 错误率统计
- [ ] 资源使用率
- [ ] 日志轮转配置


🌟 核心特性
✅ 多端口架构
每个域名分配独立端口（8081, 8082, 8083...）
完美解决相对路径问题
无需复杂的URL重写
✅ 智能代理管理
自动端口分配和管理
动态启动/停止代理服务器
实时域名状态监控
✅ 完美兼容性
浏览器原生处理相对路径
JavaScript完全兼容
无需注入任何钩子
✅ 代理支持
自动检测环境变量中的代理配置
支持HTTP/HTTPS/SOCKS5代理
已预配置127.0.0.1:7890代理

## 📦 快速开始

```bash
go build -o mirrorgo
./mirrorgo
```

然后访问：

```
http://localhost:8080/proxy/github.com
```

---

## 📁 项目结构

- `main.go` - 启动入口
- `proxy/router.go` - 处理反代逻辑
- `config/config.yaml` - 配置文件
- `static/hook.js` - 注入 JS 脚本
- `README.md` - 项目说明

---

## 🛡️ 安全建议

- 建议通过 Nginx/Caddy 启用 HTTPS；
- 配置 IP 白名单或访问认证；
- 仅允许可信目标域；

---

## 📄 License

MIT License

---




listen: ":8080"
proxyPath: "/proxy/"

# 允许代理的域名列表
allowDomains:
  - github.com
  - githubusercontent.com
  - github.githubassets.com
  - avatars.githubusercontent.com
  - camo.githubusercontent.com
  - wikipedia.org
  - upload.wikimedia.org
  - zhihu.com
  - pic1.zhimg.com
  - pic2.zhimg.com
  - pic3.zhimg.com
  - pic4.zhimg.com
  - static.zhihu.com

# 自动发现的域名模式（正则表达式）
autoDiscoverPatterns:
  - ".*\\.githubusercontent\\.com"
  - ".*\\.github\\.io"
  - ".*\\.githubassets\\.com"
  - ".*\\.wikimedia\\.org"
  - ".*\\.zhimg\\.com"

# URL重写规则
rewriteRules:
  # 需要重写的URL模式
  urlPatterns:
    - "https?://([^/]+)/(.*)"
    - "//([^/]+)/(.*)"
    - "/([^/]+)/(.*)"

  # 需要处理的内容类型
  contentTypes:
    - "text/html"
    - "text/css"
    - "application/javascript"
    - "text/javascript"
    - "application/json"

# JavaScript注入配置
jsInjection:
  enabled: true
  hookFile: "static/hook.js"

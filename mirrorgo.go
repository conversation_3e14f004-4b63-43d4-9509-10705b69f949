package main

import (
	"compress/flate"
	"compress/gzip"
	"crypto/tls"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"regexp"
	"strings"
	"sync"
	"syscall"
	"time"
)

// ProxyManager 管理所有代理服务器
type ProxyManager struct {
	domainToPort map[string]int
	portToDomain map[int]string
	servers      map[int]*http.Server
	mutex        sync.RWMutex
	basePort     int
	nextPort     int
}

// NewProxyManager 创建新的代理管理器
func NewProxyManager(basePort int) *ProxyManager {
	return &ProxyManager{
		domainToPort: make(map[string]int),
		portToDomain: make(map[int]string),
		servers:      make(map[int]*http.Server),
		basePort:     basePort,
		nextPort:     basePort,
	}
}

// GetPortForDomain 获取域名对应的端口，如果不存在则创建
func (pm *ProxyManager) GetPortForDomain(domain, protocol string) int {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 检查是否已经分配端口
	if port, exists := pm.domainToPort[domain]; exists {
		return port
	}

	// 分配新端口
	port := pm.nextPort
	pm.nextPort++

	pm.domainToPort[domain] = port
	pm.portToDomain[port] = domain

	// 启动代理服务器
	go pm.startProxyServer(port, domain, protocol)

	log.Printf("🔗 Allocated port %d for %s (%s)", port, domain, protocol)
	return port
}

// startProxyServer 启动指定端口的代理服务器
func (pm *ProxyManager) startProxyServer(port int, domain, protocol string) {
	mux := http.NewServeMux()
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		handleProxyRequest(w, r, domain, protocol, pm)
	})

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: mux,
	}

	pm.mutex.Lock()
	pm.servers[port] = server
	pm.mutex.Unlock()

	// 等待一下确保端口可用
	time.Sleep(100 * time.Millisecond)

	log.Printf("🚀 Starting proxy server for %s on port %d", domain, port)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Printf("❌ Server error for %s on port %d: %v", domain, port, err)
	}
}

// GetDomainList 获取所有域名列表
func (pm *ProxyManager) GetDomainList() map[string]int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	result := make(map[string]int)
	for domain, port := range pm.domainToPort {
		result[domain] = port
	}
	return result
}

// Shutdown 关闭所有服务器
func (pm *ProxyManager) Shutdown() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	for port, server := range pm.servers {
		log.Printf("🛑 Shutting down server on port %d", port)
		server.Close()
	}
}

// handleProxyRequest 处理代理请求
func handleProxyRequest(w http.ResponseWriter, r *http.Request, domain, protocol string, manager *ProxyManager) {
	// 构造目标URL
	targetURL := fmt.Sprintf("%s://%s%s", protocol, domain, r.URL.Path)
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	log.Printf("[%s] %s %s", domain, r.Method, r.URL.Path)

	// 创建代理请求
	proxyReq, err := http.NewRequest(r.Method, targetURL, r.Body)
	if err != nil {
		http.Error(w, "Failed to create request", http.StatusInternalServerError)
		return
	}

	// 复制请求头
	copyRequestHeaders(proxyReq, r, domain)

	// 发送请求
	client := createHTTPClient(domain)
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("[%s] Request failed: %v", domain, err)
		http.Error(w, "Failed to reach target: "+err.Error(), http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := readResponseBody(resp)
	if err != nil {
		log.Printf("[%s] Failed to read response: %v", domain, err)
		http.Error(w, "Failed to read response", http.StatusInternalServerError)
		return
	}

	// 处理内容重写
	processedBody := processResponseBody(body, resp.Header.Get("Content-Type"), r, manager)

	// 复制响应头
	copyResponseHeaders(w, resp)
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(processedBody)))

	// 写入响应
	w.WriteHeader(resp.StatusCode)
	w.Write(processedBody)
}

// copyRequestHeaders 复制请求头
func copyRequestHeaders(proxyReq *http.Request, originalReq *http.Request, domain string) {
	for name, values := range originalReq.Header {
		lowerName := strings.ToLower(name)
		if lowerName == "host" {
			proxyReq.Header.Set("Host", domain)
			continue
		}
		if strings.HasPrefix(lowerName, "x-forwarded") || lowerName == "x-real-ip" {
			continue
		}
		for _, value := range values {
			proxyReq.Header.Add(name, value)
		}
	}

	// 设置标准头部
	proxyReq.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	proxyReq.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	proxyReq.Header.Set("Accept-Language", "en-US,en;q=0.5")
	proxyReq.Header.Set("Accept-Encoding", "identity")
	proxyReq.Header.Set("DNT", "1")
	proxyReq.Header.Set("Connection", "keep-alive")
}

// copyResponseHeaders 复制响应头
func copyResponseHeaders(w http.ResponseWriter, resp *http.Response) {
	for k, vv := range resp.Header {
		lowerK := strings.ToLower(k)
		if lowerK == "content-security-policy" ||
			lowerK == "x-frame-options" ||
			lowerK == "content-encoding" ||
			lowerK == "content-length" {
			continue
		}
		for _, v := range vv {
			w.Header().Add(k, v)
		}
	}
	w.Header().Set("Content-Encoding", "identity")
}

// createHTTPClient 创建HTTP客户端
func createHTTPClient(domain string) *http.Client {
	transport := &http.Transport{
		Dial: (&net.Dialer{
			Timeout:   15 * time.Second,
			KeepAlive: 30 * time.Second,
		}).Dial,
		TLSHandshakeTimeout:   20 * time.Second,
		ResponseHeaderTimeout: 15 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		DisableCompression:    true,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
			ServerName:         domain,
		},
	}

	// 检查代理配置
	if proxyURL := getProxyURL(); proxyURL != nil {
		transport.Proxy = http.ProxyURL(proxyURL)
	}

	return &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 10 {
				return fmt.Errorf("stopped after 10 redirects")
			}
			return nil
		},
	}
}

// getProxyURL 获取代理配置
func getProxyURL() *url.URL {
	proxyEnvs := []string{"MIRRORGO_PROXY", "HTTPS_PROXY", "HTTP_PROXY", "https_proxy", "http_proxy"}
	for _, env := range proxyEnvs {
		if proxyStr := os.Getenv(env); proxyStr != "" {
			if proxyURL, err := url.Parse(proxyStr); err == nil {
				return proxyURL
			}
		}
	}
	return nil
}

// readResponseBody 读取并解压缩响应体
func readResponseBody(resp *http.Response) ([]byte, error) {
	var reader io.Reader = resp.Body

	switch strings.ToLower(resp.Header.Get("Content-Encoding")) {
	case "gzip":
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, err
		}
		defer gzipReader.Close()
		reader = gzipReader
	case "deflate":
		reader = flate.NewReader(resp.Body)
	}

	return io.ReadAll(reader)
}

// processResponseBody 处理响应体内容重写
func processResponseBody(body []byte, contentType string, req *http.Request, manager *ProxyManager) []byte {
	if !strings.Contains(contentType, "text/html") {
		return body
	}

	content := string(body)
	content = rewriteHTMLContent(content, req, manager)
	return []byte(content)
}

// rewriteHTMLContent 重写HTML内容中的URL
func rewriteHTMLContent(content string, req *http.Request, manager *ProxyManager) string {
	// 重写各种URL引用
	patterns := []struct {
		regex *regexp.Regexp
		desc  string
	}{
		{regexp.MustCompile(`href="https?://([^"]+)"`), "href"},
		{regexp.MustCompile(`src="https?://([^"]+)"`), "src"},
		{regexp.MustCompile(`action="https?://([^"]+)"`), "action"},
		{regexp.MustCompile(`url\(["']?https?://([^"')]+)["']?\)`), "css_url"},
	}

	for _, pattern := range patterns {
		content = pattern.regex.ReplaceAllStringFunc(content, func(match string) string {
			return rewriteURLMatch(match, pattern.regex, req, manager)
		})
	}

	return content
}

// rewriteURLMatch 重写匹配到的URL
func rewriteURLMatch(match string, regex *regexp.Regexp, req *http.Request, manager *ProxyManager) string {
	submatches := regex.FindStringSubmatch(match)
	if len(submatches) < 2 {
		return match
	}

	urlPart := submatches[1]
	parts := strings.SplitN(urlPart, "/", 2)
	domain := parts[0]

	// 获取该域名对应的端口
	port := manager.GetPortForDomain(domain, "https")

	// 构造新的本地URL
	newURLPart := fmt.Sprintf("localhost:%d", port)
	if len(parts) > 1 {
		newURLPart += "/" + parts[1]
	}

	return strings.Replace(match, urlPart, newURLPart, 1)
}

func main() {
	// 设置代理环境变量
	os.Setenv("http_proxy", "http://127.0.0.1:7890")
	os.Setenv("https_proxy", "http://127.0.0.1:7890")

	// 创建代理管理器，从8081端口开始
	manager := NewProxyManager(8081)

	// 创建主控制服务器
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		handleMainPage(w, r, manager)
	})
	http.HandleFunc("/proxy/", func(w http.ResponseWriter, r *http.Request) {
		handleProxyEntry(w, r, manager)
	})

	// 启动主服务器
	go func() {
		log.Println("🚀 MirrorGo Multi-Port Proxy Starting...")
		log.Println("📋 Control Panel: http://localhost:8080")
		log.Println("🔧 Proxy Ports: 8081+")
		log.Println("📖 Usage: http://localhost:8080/proxy/https/github.com")
		log.Println("")

		if err := http.ListenAndServe(":8080", nil); err != nil {
			log.Fatalf("❌ Main server failed: %v", err)
		}
	}()

	// 等待中断信号
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	log.Println("🛑 Shutting down...")
	manager.Shutdown()
	log.Println("✅ Shutdown complete")
}

// handleMainPage 显示控制面板
func handleMainPage(w http.ResponseWriter, r *http.Request, manager *ProxyManager) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}

	domains := manager.GetDomainList()

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	fmt.Fprintf(w, `<!DOCTYPE html>
<html>
<head>
    <title>MirrorGo - Multi-Port Proxy</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { color: #333; border-bottom: 3px solid #007acc; padding-bottom: 15px; margin-bottom: 30px; }
        .section { margin: 25px 0; }
        .domain-list { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }
        .domain-item { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #e0e0e0; }
        .port { color: #007acc; font-weight: bold; font-size: 1.1em; }
        .usage { background: #e8f4fd; padding: 20px; border-radius: 8px; border-left: 4px solid #17a2b8; }
        code { background: #f1f1f1; padding: 4px 8px; border-radius: 4px; font-family: 'Courier New', monospace; }
        .status { background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745; }
        .btn { display: inline-block; padding: 8px 16px; background: #007acc; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn:hover { background: #005a9e; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="header">🚀 MirrorGo - Multi-Port Proxy</h1>

        <div class="section">
            <div class="status">
                <h3>📊 System Status</h3>
                <p>✅ Control server running on port 8080</p>
                <p>🔧 Proxy ports starting from 8081</p>
                <p>📡 Active domains: %d</p>
            </div>
        </div>

        <div class="section">
            <h2>🌐 Active Domains</h2>
            <div class="domain-list">`, len(domains))

	if len(domains) == 0 {
		fmt.Fprintf(w, `                <p>No domains currently active. Start by visiting a proxy URL below!</p>`)
	} else {
		for domain, port := range domains {
			fmt.Fprintf(w, `                <div class="domain-item">
                    <strong>%s</strong> → <span class="port">localhost:%d</span>
                    <br><small>Direct access: <a href="http://localhost:%d/" target="_blank" class="btn">Open http://localhost:%d/</a></small>
                </div>`, domain, port, port, port)
		}
	}

	fmt.Fprintf(w, `            </div>
        </div>

        <div class="section">
            <h2>📖 Quick Start</h2>
            <div class="usage">
                <h3>🎯 Start a new proxy:</h3>
                <p><a href="/proxy/https/github.com" class="btn">GitHub</a></p>
                <p><a href="/proxy/https/google.com" class="btn">Google</a></p>
                <p><a href="/proxy/https/stackoverflow.com" class="btn">Stack Overflow</a></p>

                <h3>🔧 Manual URLs:</h3>
                <p><code>http://localhost:8080/proxy/https/github.com</code></p>
                <p><code>http://localhost:8080/proxy/https/any-domain.com</code></p>

                <h3>✨ Features:</h3>
                <ul>
                    <li>✅ Perfect compatibility - no URL rewriting</li>
                    <li>✅ Automatic relative path handling</li>
                    <li>✅ Smart port allocation</li>
                    <li>✅ Proxy support (configured for 127.0.0.1:7890)</li>
                    <li>✅ Real-time domain management</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>`)
}

// handleProxyEntry 处理代理入口请求
func handleProxyEntry(w http.ResponseWriter, r *http.Request, manager *ProxyManager) {
	// 解析 /proxy/protocol/domain/path
	path := strings.TrimPrefix(r.URL.Path, "/proxy/")
	parts := strings.SplitN(path, "/", 3)

	if len(parts) < 2 {
		http.Error(w, "Invalid format. Use: /proxy/https/domain.com", http.StatusBadRequest)
		return
	}

	protocol := parts[0]
	domain := parts[1]
	targetPath := "/"
	if len(parts) >= 3 {
		targetPath = "/" + parts[2]
	}

	if protocol != "http" && protocol != "https" {
		http.Error(w, "Only http and https protocols supported", http.StatusBadRequest)
		return
	}

	// 获取端口并重定向
	port := manager.GetPortForDomain(domain, protocol)
	redirectURL := fmt.Sprintf("http://localhost:%d%s", port, targetPath)
	if r.URL.RawQuery != "" {
		redirectURL += "?" + r.URL.RawQuery
	}

	log.Printf("🔄 Redirecting %s to port %d", domain, port)
	http.Redirect(w, r, redirectURL, http.StatusFound)
}

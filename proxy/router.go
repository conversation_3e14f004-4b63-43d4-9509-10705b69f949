package proxy

import (
	"compress/flate"
	"compress/gzip"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"
)

func HandleProxy(w http.ResponseWriter, r *http.Request) {
	// 解析目标URL
	target := strings.TrimPrefix(r.URL.Path, "/proxy/")
	if target == "" {
		http.Error(w, "Missing target URL", http.StatusBadRequest)
		return
	}

	// 处理URL格式 - 修复双重前缀问题
	if !strings.HasPrefix(target, "http://") && !strings.HasPrefix(target, "https://") {
		target = "https://" + target
	}

	log.Printf("Target after URL processing: %s", target)

	// 检查是否有双重协议前缀的问题 - 修复检测逻辑
	if strings.HasPrefix(target, "https://https:/") || strings.HasPrefix(target, "https://http:/") ||
		strings.HasPrefix(target, "https://https://") || strings.HasPrefix(target, "https://http://") {
		log.Printf("Warning: Double protocol prefix detected, fixing: %s", target)
		if strings.HasPrefix(target, "https://https:/") {
			// 修复：https://https:/xxx -> https://xxx
			target = "https://" + strings.TrimPrefix(target, "https://https:/")
		} else if strings.HasPrefix(target, "https://http:/") {
			// 修复：https://http:/xxx -> http://xxx
			target = "http://" + strings.TrimPrefix(target, "https://http:/")
		} else if strings.HasPrefix(target, "https://https://") {
			// 修复：https://https://xxx -> https://xxx
			target = strings.TrimPrefix(target, "https://")
		} else if strings.HasPrefix(target, "https://http://") {
			// 修复：https://http://xxx -> http://xxx
			target = strings.TrimPrefix(target, "https://")
		}
		log.Printf("Fixed target URL: %s", target)
	}

	// 添加查询参数
	if r.URL.RawQuery != "" {
		if strings.Contains(target, "?") {
			target += "&" + r.URL.RawQuery
		} else {
			target += "?" + r.URL.RawQuery
		}
	}

	targetURL, err := url.Parse(target)
	if err != nil {
		http.Error(w, "Invalid target URL: "+err.Error(), http.StatusBadRequest)
		return
	}

	log.Printf("Proxying request to: %s", targetURL.String())

	// 创建请求
	req, err := http.NewRequest(r.Method, targetURL.String(), r.Body)
	if err != nil {
		http.Error(w, "Failed to create request: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 复制请求头，但需要处理一些特殊头部
	for name, values := range r.Header {
		// 跳过一些代理相关的头部
		if strings.ToLower(name) == "host" {
			req.Header.Set("Host", targetURL.Host)
			continue
		}
		if strings.HasPrefix(strings.ToLower(name), "x-forwarded") ||
			strings.ToLower(name) == "x-real-ip" {
			continue
		}

		for _, value := range values {
			req.Header.Add(name, value)
		}
	}

	// 设置一些必要的头部
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	// 修改：请求未压缩的内容，避免解压缩问题
	req.Header.Set("Accept-Encoding", "identity")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// 发送请求 - 添加超时配置
	client := &http.Client{
		Timeout: 30 * time.Second, // 30秒超时
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 允许重定向，但限制次数
			if len(via) >= 10 {
				return fmt.Errorf("stopped after 10 redirects")
			}
			return nil
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to reach target %s: %v", targetURL.String(), err)
		http.Error(w, "Failed to reach target: "+err.Error(), http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	// 处理响应体的解压缩
	var reader io.Reader = resp.Body
	contentEncoding := resp.Header.Get("Content-Encoding")

	switch strings.ToLower(contentEncoding) {
	case "gzip":
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			log.Printf("Failed to create gzip reader: %v", err)
			http.Error(w, "Failed to decompress gzip content", http.StatusInternalServerError)
			return
		}
		defer gzipReader.Close()
		reader = gzipReader
		log.Printf("Decompressing gzip content from %s", targetURL.Host)

	case "deflate":
		reader = flate.NewReader(resp.Body)
		log.Printf("Decompressing deflate content from %s", targetURL.Host)

	case "br":
		// Brotli压缩暂时不支持，使用原始内容
		log.Printf("Warning: Brotli compression not supported, using raw content")
		reader = resp.Body

	default:
		// 无压缩或未知压缩格式
		reader = resp.Body
	}

	// 读取响应内容
	body, err := io.ReadAll(reader)
	if err != nil {
		log.Printf("Failed to read response body: %v", err)
		http.Error(w, "Failed to read response: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 获取内容类型
	contentType := resp.Header.Get("Content-Type")
	log.Printf("Content-Type: %s, Content-Length: %d bytes", contentType, len(body))

	// 处理内容重写（仅对HTML进行简单重写）
	processedBody := body
	if strings.Contains(contentType, "text/html") {
		// 创建简单的URL重写器
		proxyHost := "http://" + r.Host
		if r.TLS != nil {
			proxyHost = "https://" + r.Host
		}

		rewriter := NewSimpleURLRewriter(proxyHost, "/proxy/")
		content := string(body)

		// 重写HTML中的URL
		content = rewriter.RewriteHTML(content, targetURL)

		// 注入简单的JavaScript钩子
		content = rewriter.InjectSimpleJS(content)

		processedBody = []byte(content)
		log.Printf("Applied URL rewriting for HTML content from %s", targetURL.Host)
	}

	// 复制响应头，但需要处理一些特殊头部
	for k, vv := range resp.Header {
		// 跳过一些可能导致问题的头部
		lowerK := strings.ToLower(k)
		if lowerK == "content-security-policy" ||
			lowerK == "x-frame-options" ||
			lowerK == "content-encoding" || // 跳过，因为我们已经解压缩了
			lowerK == "content-length" { // 跳过，因为内容长度可能已经改变
			continue
		}

		for _, v := range vv {
			w.Header().Add(k, v)
		}
	}

	// 设置正确的内容长度和编码
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(processedBody)))
	w.Header().Set("Content-Encoding", "identity") // 明确表示未压缩

	// 写入状态码和内容
	w.WriteHeader(resp.StatusCode)
	w.Write(processedBody)
}

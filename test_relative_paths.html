<!DOCTYPE html>
<html>
<head>
    <title>Test Relative Paths</title>
    <style>
        @font-face {
            font-family: 'TestFont';
            src: url('/assets/mona-sans-d1bf285e9b9b.woff2') format('woff2');
        }
        .test {
            background: url('/assets/test-image.png');
        }
    </style>
    <link rel="stylesheet" href="/assets/test.css">
</head>
<body>
    <h1>Test Relative Paths</h1>
    <img src="/assets/test-image.png" alt="Test">
    <script src="/assets/test.js"></script>
    <script>
        // Test JavaScript URL rewriting
        fetch('/assets/data.json').then(r => r.json());
        const img = new Image();
        img.src = '/assets/another-image.png';
    </script>
</body>
</html>

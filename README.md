# 🚀 MirrorGo - 预配置多端口代理系统

一个基于Go的多端口网站代理系统，通过预配置端口映射实现精确的域名代理控制，专为部署到Caddy反向代理环境设计。

## 🎯 设计理念

### **预配置 > 智能判断**
- ✅ 手动配置重要域名和端口映射
- ✅ 精确控制哪些域名需要代理
- ✅ 避免不必要的端口分配
- ✅ 简单、可预测、易维护

### **核心功能 > 全站镜像**
- 🎯 专注网站核心功能（如GitHub的代码浏览、搜索）
- 🚫 忽略外部链接（社交媒体、项目官网等）
- 📊 资源高效利用
- 🔧 易于调试和维护

## 竞品
- Ultraviolet


## ✨ 特性 Features

- 🌐 多域名全站镜像反代
- 🧩 HTML/CSS/JS 动态重写（下一步实现）
- 🚀 高性能 Go 原生实现
- 🔐 智能防盗链绕过
- 🔧 访问控制 / 限速 / 日志记录

---
###  页面引用了多个外域资源（如静态资源、头像图像、JS 脚本等），这些也往往被墙
✅ 解决思路：全域名多域反代 + HTML/JS 动态重写

- 为了让页面完整可用，需要做到：

- 自动识别并转发所有相关域名的资源；

- 自动将 HTML、CSS、JS 中引用的 URL 进行“重写”，变成可通过代理访问的形式；

- 最好还要避免使用相对路径导致的跨域问题；

- 对于跳转、表单提交等也要保持代理状态。


🌟 核心特性
✅ 多端口架构
每个域名分配独立端口（8081, 8082, 8083...）
完美解决相对路径问题
无需复杂的URL重写
✅ 智能代理管理
自动端口分配和管理
动态启动/停止代理服务器
实时域名状态监控
✅ 完美兼容性
浏览器原生处理相对路径
JavaScript完全兼容
无需注入任何钩子
✅ 代理支持
自动检测环境变量中的代理配置
支持HTTP/HTTPS/SOCKS5代理
已预配置127.0.0.1:7890代理

## 📦 快速开始

```bash
go build -o mirrorgo
./mirrorgo
```

然后访问：

```
http://localhost:8080/proxy/github.com
```

---

## 📁 项目结构

- `main.go` - 启动入口
- `proxy/router.go` - 处理反代逻辑
- `config/config.yaml` - 配置文件
- `static/hook.js` - 注入 JS 脚本
- `README.md` - 项目说明

---

## 🛡️ 安全建议

- 建议通过 Nginx/Caddy 启用 HTTPS；
- 配置 IP 白名单或访问认证；
- 仅允许可信目标域；

---

## 📄 License

MIT License

---




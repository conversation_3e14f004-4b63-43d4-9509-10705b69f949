package proxy

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
)

type SimpleURLRewriter struct {
	proxyHost string
	proxyPath string
}

func NewSimpleURLRewriter(proxyHost, proxyPath string) *SimpleURLRewriter {
	return &SimpleURLRewriter{
		proxyHost: proxyHost,
		proxyPath: proxyPath,
	}
}

// RewriteHTML 重写HTML内容中的URL，使用新的 /proxy/protocol/domain/path 格式
func (r *SimpleURLRewriter) RewriteHTML(content string, baseURL *url.URL) string {
	// HTML中常见的URL属性模式
	patterns := []struct {
		regex *regexp.Regexp
		name  string
	}{
		{regexp.MustCompile(`(<[^>]*\s)(href|src|action|data-[^=]*|content)(\s*=\s*["'])([^"']+)(["'][^>]*>)`), "html_attr"},
		{regexp.MustCompile(`(@import\s+["']?)([^"';\s]+)(["']?[^;]*;?)`), "css_import"},
		{regexp.MustCompile(`(url\s*\(\s*["']?)([^"')]+)(["']?\s*\))`), "css_url"},
	}

	result := content
	for _, pattern := range patterns {
		result = pattern.regex.ReplaceAllStringFunc(result, func(match string) string {
			return r.rewriteURLInMatch(match, pattern.regex, baseURL)
		})
	}

	return result
}

// RewriteCSS 重写CSS内容中的URL
func (r *SimpleURLRewriter) RewriteCSS(content string, baseURL *url.URL) string {
	patterns := []*regexp.Regexp{
		regexp.MustCompile(`(@import\s+["']?)([^"';\s]+)(["']?[^;]*;?)`),
		regexp.MustCompile(`(url\s*\(\s*["']?)([^"')]+)(["']?\s*\))`),
	}

	result := content
	for _, pattern := range patterns {
		result = pattern.ReplaceAllStringFunc(result, func(match string) string {
			return r.rewriteURLInMatch(match, pattern, baseURL)
		})
	}

	return result
}

// rewriteURLInMatch 在匹配的字符串中重写URL
func (r *SimpleURLRewriter) rewriteURLInMatch(match string, pattern *regexp.Regexp, baseURL *url.URL) string {
	submatches := pattern.FindStringSubmatch(match)
	if len(submatches) < 3 {
		return match
	}

	// 找到URL部分（通常是第二个或更后的捕获组）
	var urlPart string
	for i := 1; i < len(submatches); i++ {
		if r.looksLikeURL(submatches[i]) {
			urlPart = submatches[i]
			break
		}
	}

	if urlPart == "" {
		return match
	}

	// 重写URL
	rewrittenURL := r.RewriteURL(urlPart, baseURL)
	if rewrittenURL == urlPart {
		return match // 没有变化
	}

	// 替换原始匹配中的URL部分
	result := strings.Replace(match, urlPart, rewrittenURL, 1)
	return result
}

// RewriteURL 重写单个URL，使用新的 /proxy/protocol/domain/path 格式
func (r *SimpleURLRewriter) RewriteURL(rawURL string, baseURL *url.URL) string {
	// 跳过已经是代理URL的情况
	if strings.Contains(rawURL, r.proxyPath) {
		return rawURL
	}

	// 跳过特殊协议
	if strings.HasPrefix(rawURL, "data:") ||
		strings.HasPrefix(rawURL, "javascript:") ||
		strings.HasPrefix(rawURL, "mailto:") ||
		strings.HasPrefix(rawURL, "tel:") ||
		strings.HasPrefix(rawURL, "blob:") ||
		strings.HasPrefix(rawURL, "#") {
		return rawURL
	}

	// 解析URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return rawURL
	}

	// 处理相对URL
	if parsedURL.Host == "" {
		if baseURL != nil {
			parsedURL = baseURL.ResolveReference(parsedURL)
		} else {
			return rawURL
		}
	}

	// 确定协议
	protocol := parsedURL.Scheme
	if protocol == "" {
		protocol = "https" // 默认使用https
	}

	// 只处理http和https协议
	if protocol != "http" && protocol != "https" {
		return rawURL
	}

	// 构造新格式的代理URL: /proxy/protocol/domain/path
	domain := parsedURL.Host
	path := parsedURL.Path
	if path == "" {
		path = "/"
	}

	// 添加查询参数和锚点
	if parsedURL.RawQuery != "" {
		path += "?" + parsedURL.RawQuery
	}
	if parsedURL.Fragment != "" {
		path += "#" + parsedURL.Fragment
	}

	// 构造最终的代理URL
	proxyURL := fmt.Sprintf("%s%s%s/%s%s", r.proxyHost, r.proxyPath, protocol, domain, path)
	return proxyURL
}

// looksLikeURL 判断字符串是否像URL
func (r *SimpleURLRewriter) looksLikeURL(s string) bool {
	if s == "" || len(s) < 3 {
		return false
	}
	
	return strings.Contains(s, "://") ||
		strings.HasPrefix(s, "//") ||
		strings.HasPrefix(s, "/") ||
		strings.Contains(s, ".") ||
		strings.HasPrefix(s, "?") ||
		strings.HasPrefix(s, "#")
}

// InjectSimpleJS 注入简单的JavaScript钩子来处理动态请求
func (r *SimpleURLRewriter) InjectSimpleJS(htmlContent string) string {
	jsCode := fmt.Sprintf(`
// MirrorGo URL Rewriter - 新格式支持
(function() {
    const proxyHost = '%s';
    const proxyPath = '%s';
    
    // 重写URL为新格式: /proxy/protocol/domain/path
    function rewriteURL(url) {
        if (!url || url.startsWith('data:') || url.startsWith('javascript:') || 
            url.startsWith('mailto:') || url.startsWith('tel:') || url.startsWith('blob:') ||
            url.startsWith('#') || url.includes(proxyPath)) {
            return url;
        }
        
        try {
            const urlObj = new URL(url, window.location.href);
            const protocol = urlObj.protocol.replace(':', ''); // 移除冒号
            const domain = urlObj.host;
            let path = urlObj.pathname + urlObj.search + urlObj.hash;
            
            if (protocol !== 'http' && protocol !== 'https') {
                return url;
            }
            
            return proxyHost + proxyPath + protocol + '/' + domain + path;
        } catch (e) {
            return url;
        }
    }
    
    // 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
        if (typeof input === 'string') {
            input = rewriteURL(input);
        } else if (input instanceof Request) {
            input = new Request(rewriteURL(input.url), input);
        }
        return originalFetch.call(this, input, init);
    };
    
    // 拦截XMLHttpRequest
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        url = rewriteURL(url);
        return originalOpen.call(this, method, url, async, user, password);
    };
    
    console.log('MirrorGo URL Rewriter initialized with new format');
})();
`, r.proxyHost, r.proxyPath)

	// 在</head>前注入，如果没有</head>则在<body>前注入
	if strings.Contains(htmlContent, "</head>") {
		injection := fmt.Sprintf("<script type=\"text/javascript\">%s</script>\n</head>", jsCode)
		return strings.Replace(htmlContent, "</head>", injection, 1)
	} else if strings.Contains(htmlContent, "<body") {
		injection := fmt.Sprintf("<script type=\"text/javascript\">%s</script>\n", jsCode)
		bodyIndex := strings.Index(htmlContent, "<body")
		return htmlContent[:bodyIndex] + injection + htmlContent[bodyIndex:]
	}

	// 如果都没有，直接在开头注入
	injection := fmt.Sprintf("<script type=\"text/javascript\">%s</script>\n", jsCode)
	return injection + htmlContent
}

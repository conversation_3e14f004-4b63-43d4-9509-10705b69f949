# MirrorGo 开发环境配置
proxy:
  mode: "port"                    # 开发环境使用端口模式
  control_port: 8080             # 控制面板端口
  base_port: 8081               # 起始端口
  timeout: 30s                  # 请求超时
  user_agent: "MirrorGo/1.0-dev"

# 上游代理配置（你的VPN代理）
upstream_proxy:
  enabled: true
  http_proxy: "http://127.0.0.1:7890"
  https_proxy: "http://127.0.0.1:7890"

# 域名端口映射配置
domains:
  # GitHub 完整配置
  github.com:
    enabled: true
    backend_port: 8081
    protocol: https
    related_domains:
      api.github.com:
        backend_port: 8082
        protocol: https
      avatars.githubusercontent.com:
        backend_port: 8083
        protocol: https
      github.githubassets.com:
        backend_port: 8084
        protocol: https
      raw.githubusercontent.com:
        backend_port: 8085
        protocol: https
      codeload.github.com:
        backend_port: 8086
        protocol: https
      objects.githubusercontent.com:
        backend_port: 8087
        protocol: https
        
  # Google 配置（测试用）
  google.com:
    enabled: true
    backend_port: 8088
    protocol: https
    related_domains:
      www.google.com:
        backend_port: 8089
        protocol: https
      fonts.googleapis.com:
        backend_port: 8090
        protocol: https
      ajax.googleapis.com:
        backend_port: 8091
        protocol: https
        
  # Stack Overflow 配置
  stackoverflow.com:
    enabled: true
    backend_port: 8092
    protocol: https
    related_domains:
      cdn.sstatic.net:
        backend_port: 8093
        protocol: https
      stackexchange.com:
        backend_port: 8094
        protocol: https

# URL重写配置
rewrite:
  force_http: true              # 强制重写为HTTP（开发环境）
  preserve_external: true      # 保留外部链接不重写
  debug_mode: true             # 开启调试模式

# 安全配置
security:
  remove_csp: true             # 移除CSP头
  remove_xframe: true          # 移除X-Frame-Options
  remove_cors: true            # 移除CORS限制

# 日志配置
logging:
  level: "debug"               # 开发环境使用debug级别
  access_log: true             # 记录访问日志
  error_log: true              # 记录错误日志

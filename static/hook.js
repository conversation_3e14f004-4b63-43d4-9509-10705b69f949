// MirrorGo JavaScript Hook - 动态拦截和重写网络请求
(function() {
    'use strict';

    console.log('MirrorGo Hook loaded');

    // 获取当前代理路径
    const PROXY_PATH = '/proxy/';
    const CURRENT_HOST = window.location.origin;

    // URL重写函数
    function rewriteURL(url) {
        if (!url || typeof url !== 'string') return url;

        // 跳过已经是代理URL的情况
        if (url.includes(PROXY_PATH)) return url;

        // 跳过特殊协议
        if (url.startsWith('data:') ||
            url.startsWith('javascript:') ||
            url.startsWith('mailto:') ||
            url.startsWith('tel:') ||
            url.startsWith('blob:')) {
            return url;
        }

        try {
            let targetURL;

            // 处理相对URL
            if (url.startsWith('//')) {
                targetURL = 'https:' + url;
            } else if (url.startsWith('/')) {
                // 获取当前页面的原始域名
                const currentProxyURL = window.location.pathname;
                const match = currentProxyURL.match(/\/proxy\/https?:\/\/([^\/]+)/);
                if (match) {
                    targetURL = 'https://' + match[1] + url;
                } else {
                    return url;
                }
            } else if (!url.includes('://')) {
                // 相对路径
                const currentProxyURL = window.location.pathname;
                const match = currentProxyURL.match(/\/proxy\/(https?:\/\/[^\/]+)/);
                if (match) {
                    const baseURL = match[1];
                    targetURL = new URL(url, baseURL).href;
                } else {
                    return url;
                }
            } else {
                targetURL = url;
            }

            // 构造代理URL
            return CURRENT_HOST + PROXY_PATH + targetURL;
        } catch (e) {
            console.warn('URL rewrite failed:', url, e);
            return url;
        }
    }

    // 拦截fetch API
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
        let url = input;
        if (typeof input === 'object' && input.url) {
            url = input.url;
        }

        const rewrittenURL = rewriteURL(url);
        if (rewrittenURL !== url) {
            console.log('Fetch rewritten:', url, '->', rewrittenURL);
            if (typeof input === 'string') {
                return originalFetch.call(this, rewrittenURL, init);
            } else {
                const newRequest = new Request(rewrittenURL, input);
                return originalFetch.call(this, newRequest, init);
            }
        }

        return originalFetch.call(this, input, init);
    };

    // 拦截XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        const rewrittenURL = rewriteURL(url);
        if (rewrittenURL !== url) {
            console.log('XHR rewritten:', url, '->', rewrittenURL);
        }
        return originalXHROpen.call(this, method, rewrittenURL, async, user, password);
    };

    // 拦截动态创建的元素
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(this, tagName);

        // 为特定元素添加属性监听
        if (['img', 'script', 'link', 'iframe', 'video', 'audio', 'source'].includes(tagName.toLowerCase())) {
            const originalSetAttribute = element.setAttribute;
            element.setAttribute = function(name, value) {
                if (['src', 'href', 'action'].includes(name.toLowerCase())) {
                    const rewrittenValue = rewriteURL(value);
                    if (rewrittenValue !== value) {
                        console.log('Element attribute rewritten:', value, '->', rewrittenValue);
                    }
                    return originalSetAttribute.call(this, name, rewrittenValue);
                }
                return originalSetAttribute.call(this, name, value);
            };

            // 监听属性直接赋值
            ['src', 'href'].forEach(prop => {
                if (element[prop] !== undefined) {
                    let value = element[prop];
                    Object.defineProperty(element, prop, {
                        get: function() { return value; },
                        set: function(newValue) {
                            const rewrittenValue = rewriteURL(newValue);
                            if (rewrittenValue !== newValue) {
                                console.log('Element property rewritten:', newValue, '->', rewrittenValue);
                            }
                            value = rewrittenValue;
                            originalSetAttribute.call(this, prop, rewrittenValue);
                        }
                    });
                }
            });
        }

        return element;
    };

    // 拦截表单提交
    const originalSubmit = HTMLFormElement.prototype.submit;
    HTMLFormElement.prototype.submit = function() {
        if (this.action) {
            const rewrittenAction = rewriteURL(this.action);
            if (rewrittenAction !== this.action) {
                console.log('Form action rewritten:', this.action, '->', rewrittenAction);
                this.action = rewrittenAction;
            }
        }
        return originalSubmit.call(this);
    };

    // 拦截location变化
    const originalLocationAssign = window.location.assign;
    const originalLocationReplace = window.location.replace;

    window.location.assign = function(url) {
        const rewrittenURL = rewriteURL(url);
        if (rewrittenURL !== url) {
            console.log('Location assign rewritten:', url, '->', rewrittenURL);
        }
        return originalLocationAssign.call(this, rewrittenURL);
    };

    window.location.replace = function(url) {
        const rewrittenURL = rewriteURL(url);
        if (rewrittenURL !== url) {
            console.log('Location replace rewritten:', url, '->', rewrittenURL);
        }
        return originalLocationReplace.call(this, rewrittenURL);
    };

    // 监听href属性变化
    let locationHref = window.location.href;
    Object.defineProperty(window.location, 'href', {
        get: function() { return locationHref; },
        set: function(url) {
            const rewrittenURL = rewriteURL(url);
            if (rewrittenURL !== url) {
                console.log('Location href rewritten:', url, '->', rewrittenURL);
            }
            locationHref = rewrittenURL;
            window.location.assign(rewrittenURL);
        }
    });

    // 处理已存在的元素
    function processExistingElements() {
        const selectors = [
            'a[href]', 'img[src]', 'script[src]', 'link[href]',
            'iframe[src]', 'video[src]', 'audio[src]', 'source[src]',
            'form[action]'
        ];

        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                const attr = selector.match(/\[(\w+)\]/)[1];
                const originalValue = element.getAttribute(attr);
                if (originalValue) {
                    const rewrittenValue = rewriteURL(originalValue);
                    if (rewrittenValue !== originalValue) {
                        console.log('Existing element rewritten:', originalValue, '->', rewrittenValue);
                        element.setAttribute(attr, rewrittenValue);
                    }
                }
            });
        });
    }

    // DOM加载完成后处理现有元素
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', processExistingElements);
    } else {
        processExistingElements();
    }

    // 监听动态添加的元素
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // 处理新添加的元素
                    const selectors = [
                        'a[href]', 'img[src]', 'script[src]', 'link[href]',
                        'iframe[src]', 'video[src]', 'audio[src]', 'source[src]',
                        'form[action]'
                    ];

                    selectors.forEach(selector => {
                        if (node.matches && node.matches(selector)) {
                            const attr = selector.match(/\[(\w+)\]/)[1];
                            const originalValue = node.getAttribute(attr);
                            if (originalValue) {
                                const rewrittenValue = rewriteURL(originalValue);
                                if (rewrittenValue !== originalValue) {
                                    console.log('Dynamic element rewritten:', originalValue, '->', rewrittenValue);
                                    node.setAttribute(attr, rewrittenValue);
                                }
                            }
                        }

                        // 处理子元素
                        if (node.querySelectorAll) {
                            node.querySelectorAll(selector).forEach(element => {
                                const attr = selector.match(/\[(\w+)\]/)[1];
                                const originalValue = element.getAttribute(attr);
                                if (originalValue) {
                                    const rewrittenValue = rewriteURL(originalValue);
                                    if (rewrittenValue !== originalValue) {
                                        console.log('Dynamic child element rewritten:', originalValue, '->', rewrittenValue);
                                        element.setAttribute(attr, rewrittenValue);
                                    }
                                }
                            });
                        }
                    });
                }
            });
        });
    });

    observer.observe(document.body || document.documentElement, {
        childList: true,
        subtree: true
    });

    console.log('MirrorGo Hook initialization complete');
})();

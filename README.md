# 🧬 MirrorGo：高性能网站镜像反向代理系统

> 🚀 基于 Golang 编写，专为实现完整网站镜像与加速访问而设计  
> 🎯 目标：绕过封锁、实现动态重写、保持原站体验、稳定可维护

---

## 竞品
- Ultraviolet


## ✨ 特性 Features

- 🌐 多域名全站镜像反代
- 🧩 HTML/CSS/JS 动态重写（下一步实现）
- 🚀 高性能 Go 原生实现
- 🔐 智能防盗链绕过
- 🔧 访问控制 / 限速 / 日志记录

---
###  页面引用了多个外域资源（如静态资源、头像图像、JS 脚本等），这些也往往被墙
✅ 解决思路：全域名多域反代 + HTML/JS 动态重写

- 为了让页面完整可用，需要做到：

- 自动识别并转发所有相关域名的资源；

- 自动将 HTML、CSS、JS 中引用的 URL 进行“重写”，变成可通过代理访问的形式；

- 最好还要避免使用相对路径导致的跨域问题；

- 对于跳转、表单提交等也要保持代理状态。



## 📦 快速开始

```bash
go build -o mirrorgo
./mirrorgo
```

然后访问：

```
http://localhost:8080/proxy/github.com
```

---

## 📁 项目结构

- `main.go` - 启动入口
- `proxy/router.go` - 处理反代逻辑
- `config/config.yaml` - 配置文件
- `static/hook.js` - 注入 JS 脚本
- `README.md` - 项目说明

---

## 🛡️ 安全建议

- 建议通过 Nginx/Caddy 启用 HTTPS；
- 配置 IP 白名单或访问认证；
- 仅允许可信目标域；

---

## 📄 License

MIT License

---




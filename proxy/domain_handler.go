package proxy

import (
	"compress/flate"
	"compress/gzip"
	"crypto/tls"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"
)

// DomainProxyHandler 处理特定域名的代理请求
type DomainProxyHandler struct {
	targetDomain   string
	targetProtocol string
	manager        *DomainPortManager
}

// ServeHTTP 实现http.Handler接口
func (h *DomainProxyHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// 构造目标URL
	targetURL := fmt.Sprintf("%s://%s%s", h.targetProtocol, h.targetDomain, r.URL.Path)
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}

	log.Printf("[%s] %s %s -> %s", h.targetDomain, r.Method, r.URL.Path, targetURL)

	// 创建代理请求
	proxyReq, err := http.NewRequest(r.Method, targetURL, r.Body)
	if err != nil {
		http.Error(w, "Failed to create proxy request: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 复制请求头
	h.copyRequestHeaders(proxyReq, r)

	// 发送请求
	client := h.createHTTPClient()
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("[%s] Request failed: %v", h.targetDomain, err)
		http.Error(w, "Failed to reach target: "+err.Error(), http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := h.readResponseBody(resp)
	if err != nil {
		log.Printf("[%s] Failed to read response: %v", h.targetDomain, err)
		http.Error(w, "Failed to read response: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 处理内容重写
	processedBody := h.processResponseBody(body, resp.Header.Get("Content-Type"), r)

	// 复制响应头
	h.copyResponseHeaders(w, resp)

	// 设置正确的内容长度
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(processedBody)))

	// 写入响应
	w.WriteHeader(resp.StatusCode)
	w.Write(processedBody)

	log.Printf("[%s] Response: %d, %d bytes", h.targetDomain, resp.StatusCode, len(processedBody))
}

// copyRequestHeaders 复制请求头
func (h *DomainProxyHandler) copyRequestHeaders(proxyReq *http.Request, originalReq *http.Request) {
	for name, values := range originalReq.Header {
		// 跳过一些代理相关的头部
		lowerName := strings.ToLower(name)
		if lowerName == "host" {
			proxyReq.Header.Set("Host", h.targetDomain)
			continue
		}
		if strings.HasPrefix(lowerName, "x-forwarded") || lowerName == "x-real-ip" {
			continue
		}

		for _, value := range values {
			proxyReq.Header.Add(name, value)
		}
	}

	// 设置标准请求头
	proxyReq.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	proxyReq.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	proxyReq.Header.Set("Accept-Language", "en-US,en;q=0.5")
	proxyReq.Header.Set("Accept-Encoding", "identity") // 避免压缩问题
	proxyReq.Header.Set("DNT", "1")
	proxyReq.Header.Set("Connection", "keep-alive")
	proxyReq.Header.Set("Upgrade-Insecure-Requests", "1")
}

// copyResponseHeaders 复制响应头
func (h *DomainProxyHandler) copyResponseHeaders(w http.ResponseWriter, resp *http.Response) {
	for k, vv := range resp.Header {
		lowerK := strings.ToLower(k)
		// 跳过一些可能导致问题的头部
		if lowerK == "content-security-policy" ||
			lowerK == "x-frame-options" ||
			lowerK == "content-encoding" ||
			lowerK == "content-length" {
			continue
		}

		for _, v := range vv {
			w.Header().Add(k, v)
		}
	}

	w.Header().Set("Content-Encoding", "identity")
}

// createHTTPClient 创建HTTP客户端
func (h *DomainProxyHandler) createHTTPClient() *http.Client {
	transport := &http.Transport{
		Dial: (&net.Dialer{
			Timeout:   15 * time.Second,
			KeepAlive: 30 * time.Second,
		}).Dial,
		TLSHandshakeTimeout:   20 * time.Second,
		ResponseHeaderTimeout: 15 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		DisableCompression:    true,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
			ServerName:         h.targetDomain,
		},
	}

	// 检查代理配置
	if proxyURL := h.getProxyURL(); proxyURL != nil {
		transport.Proxy = http.ProxyURL(proxyURL)
	}

	return &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 10 {
				return fmt.Errorf("stopped after 10 redirects")
			}
			return nil
		},
	}
}

// getProxyURL 获取代理配置
func (h *DomainProxyHandler) getProxyURL() *url.URL {
	proxyEnvs := []string{"MIRRORGO_PROXY", "HTTPS_PROXY", "HTTP_PROXY", "https_proxy", "http_proxy"}

	for _, env := range proxyEnvs {
		if proxyStr := os.Getenv(env); proxyStr != "" {
			if proxyURL, err := url.Parse(proxyStr); err == nil {
				return proxyURL
			}
		}
	}
	return nil
}

// readResponseBody 读取并解压缩响应体
func (h *DomainProxyHandler) readResponseBody(resp *http.Response) ([]byte, error) {
	var reader io.Reader = resp.Body

	switch strings.ToLower(resp.Header.Get("Content-Encoding")) {
	case "gzip":
		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, err
		}
		defer gzipReader.Close()
		reader = gzipReader

	case "deflate":
		reader = flate.NewReader(resp.Body)
	}

	return io.ReadAll(reader)
}

// processResponseBody 处理响应体内容重写
func (h *DomainProxyHandler) processResponseBody(body []byte, contentType string, req *http.Request) []byte {
	if !strings.Contains(contentType, "text/html") {
		return body // 非HTML内容直接返回
	}

	content := string(body)

	// 重写HTML中的URL引用
	content = h.rewriteHTMLContent(content, req)

	return []byte(content)
}

// rewriteHTMLContent 重写HTML内容中的URL
func (h *DomainProxyHandler) rewriteHTMLContent(content string, req *http.Request) string {
	// 获取当前请求的主机信息
	host := req.Host
	if host == "" {
		host = "localhost"
	}

	// 重写各种URL引用
	patterns := []struct {
		regex *regexp.Regexp
		desc  string
	}{
		// href属性
		{regexp.MustCompile(`href="https?://([^"]+)"`), "href"},
		// src属性
		{regexp.MustCompile(`src="https?://([^"]+)"`), "src"},
		// action属性
		{regexp.MustCompile(`action="https?://([^"]+)"`), "action"},
		// CSS中的url()
		{regexp.MustCompile(`url\(["']?https?://([^"')]+)["']?\)`), "css_url"},
		// JavaScript中的URL
		{regexp.MustCompile(`["']https?://([^"']+)["']`), "js_url"},
	}

	for _, pattern := range patterns {
		content = pattern.regex.ReplaceAllStringFunc(content, func(match string) string {
			return h.rewriteURLMatch(match, pattern.regex, host)
		})
	}

	return content
}

// rewriteURLMatch 重写匹配到的URL
func (h *DomainProxyHandler) rewriteURLMatch(match string, regex *regexp.Regexp, currentHost string) string {
	submatches := regex.FindStringSubmatch(match)
	if len(submatches) < 2 {
		return match
	}

	// 提取域名和路径
	urlPart := submatches[1]
	parts := strings.SplitN(urlPart, "/", 2)
	domain := parts[0]
	path := "/"
	if len(parts) > 1 {
		path = "/" + parts[1]
	}

	// 获取该域名对应的端口
	port, err := h.manager.GetPortForDomain(domain, "https")
	if err != nil {
		log.Printf("Failed to get port for domain %s: %v", domain, err)
		return match // 保持原样
	}

	// 构造新的本地URL
	newURLPart := fmt.Sprintf("%s:%d%s", currentHost, port, path)

	// 替换原始URL
	return strings.Replace(match, urlPart, newURLPart, 1)
}

package proxy

import (
	"fmt"
	"mirrorgo/config"
	"net/url"
	"regexp"
	"strings"
)

type URLRewriter struct {
	proxyHost string
	proxyPath string
}

func NewURLRewriter(proxyHost, proxyPath string) *URLRewriter {
	return &URLRewriter{
		proxyHost: proxyHost,
		proxyPath: proxyPath,
	}
}

// RewriteHTML 重写HTML内容中的URL
func (r *URLRewriter) RewriteHTML(content string, baseURL *url.URL) string {
	// HTML中常见的URL属性模式
	patterns := []struct {
		regex *regexp.Regexp
		attr  string
	}{
		{regexp.MustCompile(`(<[^>]*\s)(href|src|action|data-[^=]*|content)(\s*=\s*["'])([^"']+)(["'][^>]*>)`), ""},
		{regexp.MustCompile(`(@import\s+["']?)([^"';\s]+)(["']?[^;]*;?)`), "css"},
		{regexp.MustCompile(`(url\s*\(\s*["']?)([^"')]+)(["']?\s*\))`), "css"},
	}

	result := content
	for _, pattern := range patterns {
		result = pattern.regex.ReplaceAllStringFunc(result, func(match string) string {
			return r.rewriteURLInMatch(match, pattern.regex, baseURL)
		})
	}

	return result
}

// RewriteCSS 重写CSS内容中的URL
func (r *URLRewriter) RewriteCSS(content string, baseURL *url.URL) string {
	// CSS中的URL模式
	patterns := []*regexp.Regexp{
		regexp.MustCompile(`(@import\s+["']?)([^"';\s]+)(["']?[^;]*;?)`),
		regexp.MustCompile(`(url\s*\(\s*["']?)([^"')]+)(["']?\s*\))`),
	}

	result := content
	for _, pattern := range patterns {
		result = pattern.ReplaceAllStringFunc(result, func(match string) string {
			return r.rewriteURLInMatch(match, pattern, baseURL)
		})
	}

	return result
}

// RewriteJS 重写JavaScript内容中的URL
func (r *URLRewriter) RewriteJS(content string, baseURL *url.URL) string {
	// JavaScript中的URL模式
	patterns := []*regexp.Regexp{
		// fetch(), XMLHttpRequest等API调用
		regexp.MustCompile(`(fetch\s*\(\s*["']?)([^"']+)(["']?[^)]*\))`),
		regexp.MustCompile(`(\.open\s*\(\s*["'][^"']*["']\s*,\s*["']?)([^"']+)(["']?[^)]*\))`),
		// 字符串中的URL
		regexp.MustCompile(`(["'])((https?:)?//[^"']+)(["'])`),
		// location相关
		regexp.MustCompile(`(location\.href\s*=\s*["']?)([^"']+)(["']?)`),
		regexp.MustCompile(`(window\.location\s*=\s*["']?)([^"']+)(["']?)`),
	}

	result := content
	for _, pattern := range patterns {
		result = pattern.ReplaceAllStringFunc(result, func(match string) string {
			return r.rewriteURLInMatch(match, pattern, baseURL)
		})
	}

	return result
}

// rewriteURLInMatch 在匹配的字符串中重写URL
func (r *URLRewriter) rewriteURLInMatch(match string, pattern *regexp.Regexp, baseURL *url.URL) string {
	submatches := pattern.FindStringSubmatch(match)
	if len(submatches) < 3 {
		return match
	}

	// 找到URL部分（通常是第二个捕获组）
	var urlPart string
	for i := 1; i < len(submatches); i++ {
		if r.looksLikeURL(submatches[i]) {
			urlPart = submatches[i]
			break
		}
	}

	if urlPart == "" {
		return match
	}

	// 重写URL
	rewrittenURL := r.RewriteURL(urlPart, baseURL)
	if rewrittenURL == urlPart {
		return match // 没有变化
	}

	// 替换原始匹配中的URL部分
	result := match
	result = strings.Replace(result, urlPart, rewrittenURL, 1)

	return result
}

// RewriteURL 重写单个URL
func (r *URLRewriter) RewriteURL(rawURL string, baseURL *url.URL) string {
	// 跳过已经是代理URL的情况
	if strings.Contains(rawURL, r.proxyPath) {
		return rawURL
	}

	// 跳过特殊协议
	if strings.HasPrefix(rawURL, "data:") ||
		strings.HasPrefix(rawURL, "javascript:") ||
		strings.HasPrefix(rawURL, "mailto:") ||
		strings.HasPrefix(rawURL, "tel:") {
		return rawURL
	}

	// 解析URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return rawURL
	}

	// 处理相对URL
	if parsedURL.Host == "" {
		if baseURL != nil {
			parsedURL = baseURL.ResolveReference(parsedURL)
		} else {
			return rawURL
		}
	}

	// 检查是否需要代理
	if !config.GlobalConfig.IsAllowedDomain(parsedURL.Host) {
		return rawURL
	}

	// 构造代理URL
	proxyURL := fmt.Sprintf("%s%s%s", r.proxyHost, r.proxyPath, parsedURL.String())
	return proxyURL
}

// looksLikeURL 判断字符串是否像URL
func (r *URLRewriter) looksLikeURL(s string) bool {
	return strings.Contains(s, "://") ||
		strings.HasPrefix(s, "//") ||
		strings.HasPrefix(s, "/") ||
		strings.Contains(s, ".")
}

// InjectJavaScript 注入JavaScript钩子
func (r *URLRewriter) InjectJavaScript(htmlContent string, jsContent string) string {
	// 在</head>前注入，如果没有</head>则在<body>前注入
	if strings.Contains(htmlContent, "</head>") {
		injection := fmt.Sprintf("<script type=\"text/javascript\">\n%s\n</script>\n</head>", jsContent)
		return strings.Replace(htmlContent, "</head>", injection, 1)
	} else if strings.Contains(htmlContent, "<body") {
		injection := fmt.Sprintf("<script type=\"text/javascript\">\n%s\n</script>\n", jsContent)
		bodyIndex := strings.Index(htmlContent, "<body")
		return htmlContent[:bodyIndex] + injection + htmlContent[bodyIndex:]
	}

	// 如果都没有，直接在开头注入
	injection := fmt.Sprintf("<script type=\"text/javascript\">\n%s\n</script>\n", jsContent)
	return injection + htmlContent
}

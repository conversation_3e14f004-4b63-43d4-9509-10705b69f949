package main

import (
	"log"
	"mirrorgo/config"
	"mirrorgo/proxy"
	"net/http"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置路由
	http.HandleFunc(cfg.ProxyPath, proxy.HandleProxy)

	// 启动服务器
	log.Printf("MirrorGo is running on %s", cfg.Listen)
	log.Printf("Proxy path: %s", cfg.ProxyPath)
	log.Printf("Allowed domains: %v", cfg.AllowDomains)

	if err := http.ListenAndServe(cfg.Listen, nil); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

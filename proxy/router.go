package proxy

import (
	"fmt"
	"io"
	"log"
	"mirrorgo/config"
	"net/http"
	"net/url"
	"os"
	"strings"
)

func HandleProxy(w http.ResponseWriter, r *http.Request) {
	// 解析目标URL
	target := strings.TrimPrefix(r.URL.Path, "/proxy/")
	if target == "" {
		http.Error(w, "Missing target URL", http.StatusBadRequest)
		return
	}

	// 处理URL格式
	if !strings.HasPrefix(target, "http") {
		target = "https://" + target
	}

	// 添加查询参数
	if r.URL.RawQuery != "" {
		if strings.Contains(target, "?") {
			target += "&" + r.URL.RawQuery
		} else {
			target += "?" + r.URL.RawQuery
		}
	}

	targetURL, err := url.Parse(target)
	if err != nil {
		http.Error(w, "Invalid target URL: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 检查域名是否允许
	if !config.GlobalConfig.IsAllowedDomain(targetURL.Host) {
		http.Error(w, "Domain not allowed: "+targetURL.Host, http.StatusForbidden)
		return
	}

	// 创建请求
	req, err := http.NewRequest(r.Method, targetURL.String(), r.Body)
	if err != nil {
		http.Error(w, "Failed to create request: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 复制请求头，但需要处理一些特殊头部
	for name, values := range r.Header {
		// 跳过一些代理相关的头部
		if strings.ToLower(name) == "host" {
			req.Header.Set("Host", targetURL.Host)
			continue
		}
		if strings.HasPrefix(strings.ToLower(name), "x-forwarded") ||
		   strings.ToLower(name) == "x-real-ip" {
			continue
		}
		
		for _, value := range values {
			req.Header.Add(name, value)
		}
	}

	// 设置一些必要的头部
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// 发送请求
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 允许重定向，但限制次数
			if len(via) >= 10 {
				return fmt.Errorf("stopped after 10 redirects")
			}
			return nil
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to reach target %s: %v", targetURL.String(), err)
		http.Error(w, "Failed to reach target: "+err.Error(), http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		http.Error(w, "Failed to read response: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 获取内容类型
	contentType := resp.Header.Get("Content-Type")
	
	// 复制响应头，但需要处理一些特殊头部
	for k, vv := range resp.Header {
		// 跳过一些可能导致问题的头部
		lowerK := strings.ToLower(k)
		if lowerK == "content-security-policy" ||
		   lowerK == "x-frame-options" ||
		   lowerK == "content-encoding" ||
		   lowerK == "content-length" {
			continue
		}
		
		for _, v := range vv {
			w.Header().Add(k, v)
		}
	}

	// 处理内容重写
	var processedBody []byte
	if config.GlobalConfig.ShouldRewriteContent(contentType) {
		processedBody = processContent(body, contentType, targetURL, r)
	} else {
		processedBody = body
	}

	// 设置正确的内容长度
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(processedBody)))
	
	// 写入状态码和内容
	w.WriteHeader(resp.StatusCode)
	w.Write(processedBody)
}

// processContent 处理需要重写的内容
func processContent(body []byte, contentType string, targetURL *url.URL, r *http.Request) []byte {
	content := string(body)
	
	// 创建URL重写器
	proxyHost := "http://" + r.Host
	if r.TLS != nil {
		proxyHost = "https://" + r.Host
	}
	
	rewriter := NewURLRewriter(proxyHost, config.GlobalConfig.ProxyPath)
	
	// 根据内容类型进行不同的处理
	switch {
	case strings.Contains(contentType, "text/html"):
		content = rewriter.RewriteHTML(content, targetURL)
		
		// 注入JavaScript钩子
		if config.GlobalConfig.JSInjection.Enabled {
			jsContent, err := os.ReadFile(config.GlobalConfig.JSInjection.HookFile)
			if err != nil {
				log.Printf("Failed to read hook file: %v", err)
			} else {
				content = rewriter.InjectJavaScript(content, string(jsContent))
			}
		}
		
	case strings.Contains(contentType, "text/css"):
		content = rewriter.RewriteCSS(content, targetURL)
		
	case strings.Contains(contentType, "javascript") || strings.Contains(contentType, "application/javascript"):
		content = rewriter.RewriteJS(content, targetURL)
	}
	
	return []byte(content)
}

package config

import (
	"log"
	"os"
	"regexp"

	"gopkg.in/yaml.v2"
)

type Config struct {
	Listen               string            `yaml:"listen"`
	ProxyPath            string            `yaml:"proxyPath"`
	AllowDomains         []string          `yaml:"allowDomains"`
	AutoDiscoverPatterns []string          `yaml:"autoDiscoverPatterns"`
	RewriteRules         RewriteRules      `yaml:"rewriteRules"`
	JSInjection          JSInjectionConfig `yaml:"jsInjection"`

	// 编译后的正则表达式缓存
	CompiledPatterns []*regexp.Regexp `yaml:"-"`
}

type RewriteRules struct {
	URLPatterns  []string `yaml:"urlPatterns"`
	ContentTypes []string `yaml:"contentTypes"`
}

type JSInjectionConfig struct {
	Enabled  bool   `yaml:"enabled"`
	HookFile string `yaml:"hookFile"`
}

var GlobalConfig *Config

func LoadConfig(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	config := &Config{}
	err = yaml.Unmarshal(data, config)
	if err != nil {
		return nil, err
	}

	// 编译正则表达式
	for _, pattern := range config.AutoDiscoverPatterns {
		compiled, err := regexp.Compile(pattern)
		if err != nil {
			log.Printf("Warning: Invalid regex pattern %s: %v", pattern, err)
			continue
		}
		config.CompiledPatterns = append(config.CompiledPatterns, compiled)
	}

	GlobalConfig = config
	return config, nil
}

func (c *Config) IsAllowedDomain(domain string) bool {
	// 检查明确允许的域名
	for _, allowed := range c.AllowDomains {
		if domain == allowed {
			return true
		}
	}

	// 检查自动发现的模式
	for _, pattern := range c.CompiledPatterns {
		if pattern.MatchString(domain) {
			return true
		}
	}

	return false
}

func (c *Config) ShouldRewriteContent(contentType string) bool {
	for _, ct := range c.RewriteRules.ContentTypes {
		if contentType == ct || (ct == "text/javascript" && contentType == "application/javascript") {
			return true
		}
	}
	return false
}

package proxy

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"sync"
	"time"
)

// DomainPortManager 管理域名到端口的映射
type DomainPortManager struct {
	domainToPort map[string]int
	portToDomain map[int]string
	portPool     []int
	usedPorts    map[int]bool
	servers      map[int]*http.Server
	mutex        sync.RWMutex
	basePort     int
	maxPorts     int
}

// NewDomainPortManager 创建新的域名端口管理器
func NewDomainPortManager(basePort, maxPorts int) *DomainPortManager {
	dpm := &DomainPortManager{
		domainToPort: make(map[string]int),
		portToDomain: make(map[int]string),
		usedPorts:    make(map[int]bool),
		servers:      make(map[int]*http.Server),
		basePort:     basePort,
		maxPorts:     maxPorts,
	}

	// 初始化端口池
	dpm.initPortPool()

	// 预设常用网站的固定端口
	dpm.initCommonDomains()

	return dpm
}

// initPortPool 初始化可用端口池
func (dpm *DomainPortManager) initPortPool() {
	for i := 0; i < dpm.maxPorts; i++ {
		port := dpm.basePort + i
		dpm.portPool = append(dpm.portPool, port)
	}
	log.Printf("Initialized port pool: %d-%d (%d ports)",
		dpm.basePort, dpm.basePort+dpm.maxPorts-1, dpm.maxPorts)
}

// initCommonDomains 预设常用域名的固定端口
func (dpm *DomainPortManager) initCommonDomains() {
	commonDomains := map[string]int{
		"github.com":                    dpm.basePort + 1,  // 8081
		"avatars.githubusercontent.com": dpm.basePort + 2,  // 8082
		"github.githubassets.com":       dpm.basePort + 3,  // 8083
		"api.github.com":                dpm.basePort + 4,  // 8084
		"google.com":                    dpm.basePort + 5,  // 8085
		"www.google.com":                dpm.basePort + 6,  // 8086
		"wikipedia.org":                 dpm.basePort + 7,  // 8087
		"en.wikipedia.org":              dpm.basePort + 8,  // 8088
		"stackoverflow.com":             dpm.basePort + 9,  // 8089
		"cdn.jsdelivr.net":              dpm.basePort + 10, // 8090
	}

	for domain, port := range commonDomains {
		dpm.domainToPort[domain] = port
		dpm.portToDomain[port] = domain
		dpm.usedPorts[port] = true
		log.Printf("Reserved port %d for domain: %s", port, domain)
	}
}

// GetPortForDomain 获取域名对应的端口，如果不存在则分配新端口
func (dpm *DomainPortManager) GetPortForDomain(domain string, protocol string) (int, error) {
	dpm.mutex.Lock()
	defer dpm.mutex.Unlock()

	// 检查是否已经分配了端口
	if port, exists := dpm.domainToPort[domain]; exists {
		// 确保服务器正在运行
		if !dpm.isServerRunning(port) {
			err := dpm.startProxyServer(port, domain, protocol)
			if err != nil {
				return 0, fmt.Errorf("failed to start server for %s on port %d: %v", domain, port, err)
			}
		}
		return port, nil
	}

	// 分配新端口
	port, err := dpm.allocateNewPort()
	if err != nil {
		return 0, err
	}

	// 记录映射关系
	dpm.domainToPort[domain] = port
	dpm.portToDomain[port] = domain
	dpm.usedPorts[port] = true

	// 启动代理服务器
	err = dpm.startProxyServer(port, domain, protocol)
	if err != nil {
		// 回滚分配
		delete(dpm.domainToPort, domain)
		delete(dpm.portToDomain, port)
		dpm.usedPorts[port] = false
		return 0, fmt.Errorf("failed to start server for %s on port %d: %v", domain, port, err)
	}

	log.Printf("Allocated port %d for domain: %s (%s)", port, domain, protocol)
	return port, nil
}

// allocateNewPort 分配一个新的可用端口
func (dpm *DomainPortManager) allocateNewPort() (int, error) {
	for _, port := range dpm.portPool {
		if !dpm.usedPorts[port] {
			return port, nil
		}
	}
	return 0, fmt.Errorf("no available ports in pool")
}

// isServerRunning 检查指定端口的服务器是否正在运行
func (dpm *DomainPortManager) isServerRunning(port int) bool {
	server, exists := dpm.servers[port]
	if !exists {
		return false
	}

	// 尝试连接端口来检查服务器状态
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("localhost:%d", port), time.Second)
	if err != nil {
		// 服务器可能已停止，清理记录
		delete(dpm.servers, port)
		return false
	}
	conn.Close()

	return server != nil
}

// startProxyServer 为指定域名启动代理服务器
func (dpm *DomainPortManager) startProxyServer(port int, domain, protocol string) error {
	mux := http.NewServeMux()

	// 创建专用的代理处理器
	handler := &DomainProxyHandler{
		targetDomain:   domain,
		targetProtocol: protocol,
		manager:        dpm,
	}

	mux.Handle("/", handler)

	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", port),
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	dpm.servers[port] = server

	// 在goroutine中启动服务器
	go func() {
		log.Printf("Starting proxy server for %s (%s) on port %d", domain, protocol, port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("Server error for %s on port %d: %v", domain, port, err)
			// 清理失败的服务器记录
			dpm.mutex.Lock()
			delete(dpm.servers, port)
			dpm.mutex.Unlock()
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	return nil
}

// GetDomainList 获取所有已分配的域名列表
func (dpm *DomainPortManager) GetDomainList() map[string]int {
	dpm.mutex.RLock()
	defer dpm.mutex.RUnlock()

	result := make(map[string]int)
	for domain, port := range dpm.domainToPort {
		result[domain] = port
	}
	return result
}

// Shutdown 关闭所有代理服务器
func (dpm *DomainPortManager) Shutdown() error {
	dpm.mutex.Lock()
	defer dpm.mutex.Unlock()

	for port, server := range dpm.servers {
		log.Printf("Shutting down server on port %d", port)
		if err := server.Close(); err != nil {
			log.Printf("Error shutting down server on port %d: %v", port, err)
		}
	}

	dpm.servers = make(map[int]*http.Server)
	return nil
}
